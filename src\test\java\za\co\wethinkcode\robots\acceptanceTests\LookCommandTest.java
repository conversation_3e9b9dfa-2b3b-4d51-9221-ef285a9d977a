package za.co.wethinkcode.robots.acceptanceTests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.*;
import za.co.wethinkcode.robots.client.ClientApp;
import za.co.wethinkcode.robots.server.RobotWorldClient;
import za.co.wethinkcode.robots.server.RobotWorldJsonClient;
import za.co.wethinkcode.robots.server.World;
import za.co.wethinkcode.robots.handlers.VisibilityHandler;

import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * As a player
 * I want to be able to use the Look command
 * So that I can know what objects surround my robot(s)
 */
public class LookCommandTest {
    private final static int DEFAULT_PORT = 5001;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();
    private static Thread serverThread;

    @BeforeAll
    static void startServer() throws Exception {
        serverThread = new Thread(() -> {
            try {
                za.co.wethinkcode.robots.server.Server.main(new String[]{"-p", String.valueOf(DEFAULT_PORT), "-s", "1"});
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        serverThread.start();

        // Give server a moment to start
        Thread.sleep(2000);
    }

    @BeforeEach
    void connectToServer(){
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer(){
        serverClient.disconnect();
    }

    @Test
    void testLookCommandReturnsSurroundingObjects(){
        //Given that I am connected to a Robot Worlds server
        assertTrue(serverClient.isConnected());

        //And I have successfully launched a robot into the world
        String uniqueName = "blarn1" + UUID.randomUUID().toString();
        String launchRequest = "{" +
                "  \"robot\": \"" + uniqueName + "\"," +
                "  \"command\": \"launch\"," +
                "  \"arguments\": [\"tank\", \"5\", \"5\"]" +
                "}";
        JsonNode launchResponse = serverClient.sendRequest(launchRequest);
        assertEquals("OK", launchResponse.get("result").asText());

        //When I send a "look" command
        String lookRequest = "{" +
                "  \"robot\": \"" + uniqueName + "\"," +
                "  \"command\": \"look\"," +
                "  \"arguments\": []" +
                "}";
        JsonNode lookResponse = serverClient.sendRequest(lookRequest);
        assertEquals("OK", lookResponse.get("result").asText());

        //Then I should get a list of world objects within the robot's cross-view visibility range
        JsonNode objects;

        if (lookResponse.has("objects")) {
            objects = lookResponse.get("objects");
        } else {
            objects = lookResponse.get("data").get("objects");
        }

        assertNotNull(objects);
        assertFalse(objects.isEmpty());

        //And they should be described by compass directions (north, east, south, west) in relation to the robot
        for (JsonNode obj : objects) {
            assertNotNull(obj.get("direction"));
            String direction = obj.get("direction").asText();
            assertTrue(Set.of("NORTH", "SOUTH", "EAST", "WEST").contains(direction));
        }
    }

    @Test
    void testLookInEmptyWorld(){
        //Given that I am connected to a Robot Worlds server with the world size configured to 1x1
        assertTrue(serverClient.isConnected());

        //And I have successfully launched a robot into the world
        String uniqueName = "blarny" + UUID.randomUUID().toString();
        String launchRequest = "{" +
                "  \"robot\": \"" + uniqueName + "\"," +
                "  \"command\": \"launch\"," +
                "  \"arguments\": [\"tank\", \"5\", \"5\"]" +
                "}";
        JsonNode launchResponse = serverClient.sendRequest(launchRequest);
        assertEquals("OK", launchResponse.get("result").asText());

        //When I send a "look" command
        String lookRequest = "{" +
                "  \"robot\": \"" + uniqueName + "\"," +
                "  \"command\": \"look\"," +
                "  \"arguments\": []" +
                "}";
        JsonNode lookResponse = serverClient.sendRequest(lookRequest);
        assertEquals("OK", lookResponse.get("result").asText());

        //Then I should get a response with the message "Only objects expected in 1x1 world: World Edges."
        JsonNode objects;

        if (lookResponse.has("objects")) {
            objects = lookResponse.get("objects");
        } else {
            objects = lookResponse.get("data").get("objects");
        }

        assertNotNull(objects);
        assertFalse(objects.isEmpty());

        for (JsonNode obj : objects) {
            assertEquals("EDGE", obj.get("type").asText(), "Only objects expected in 1x1 world: World Edges");
        }
    }
}
